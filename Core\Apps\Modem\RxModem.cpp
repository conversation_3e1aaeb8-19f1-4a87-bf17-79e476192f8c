/**
 * @file    RxModem.cpp
 * @brief   
 * <AUTHOR>
 * @version 1.0
 * @date    2025-03-23
 * 
 * @copyright Copyright (c) 2025
 */

#include <string.h>
#include <stdlib.h>
#include <stdio.h>
#include "AisModem.h"
#include "RxModem.h"
#include "SysLib.h"
#include "ComLib.h"
#include "AisLib.h"

// GMSK filter coefficients
static float G_vRxGmskCoefficient[RX_GMSK_BT_0_5_FIR_N] = 
{
    0.0199, 0.0755, 0.2100, 0.4384, 0.7082, 0.9209, 1.0000, 0.9209,
    0.7082, 0.4384, 0.2100, 0.0755, 0.0199
}; // BT=0.4

#define GMSK_MAX_IMPULSE_RESPONSE   (1.0f)      // max(conv(G_vRxGmskCoefficient, G_vRxGmskCoefficient))
#define GMSK_ISI_IMPULSE_RESPONSE   (0.2857f)   // Symbol ISI value

#define FILTERED_PREAMBLE_OFFSET    (3)         // Gain offset

//============================================================================
// preamble : 110011001100110011001100
// oversampling : 5
// impulse response : gmsk
// BT : 0.4
static float G_vGmskPreamble[RX_PREAMBLE_LEN] =
{
    -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,
     1,  1,  1,  1,  1,  1,  1,  1 , 1,  1,
    -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,
     1,  1,  1,  1,  1,  1,  1,  1 , 1,  1,
    -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,
     1,  1,  1,  1,  1,  1,  1,  1 , 1,  1,
    -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,
     1,  1,  1,  1,  1,  1,  1,  1 , 1,  1,
    -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,
    -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,
    -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,
    -1, -1, -1, -1, -1,  1,  1,  1,  1,  1
};

static float G_vFilteredPreamble[] =
{
-0.0199, -0.0755, -0.2100, -0.4384, -0.7082, -0.9408, -1.0755, -1.1309,
-1.1466, -1.1466, -1.1110, -1.0000, -0.7308, -0.2698,  0.2698,  0.7308,
 1.0000,  1.1110,  1.1466,  1.1466,  1.1110,  1.0000,  0.7308,  0.2698,
-0.2698, -0.7308, -1.0000, -1.1110, -1.1466, -1.1466, -1.1110, -1.0000,
-0.7308, -0.2698,  0.2698,  0.7308,  1.0000,  1.1110,  1.1466,  1.1466,
 1.1110,  1.0000,  0.7308,  0.2698, -0.2698, -0.7308, -1.0000, -1.1110,
-1.1466, -1.1466, -1.1110, -1.0000, -0.7308, -0.2698,  0.2698,  0.7308,
 1.0000,  1.1110,  1.1466,  1.1466,  1.1110,  1.0000,  0.7308,  0.2698,
-0.2698, -0.7308, -1.0000, -1.1110, -1.1466, -1.1466, -1.1110, -1.0000,
-0.7308, -0.2698,  0.2698,  0.7308,  1.0000,  1.1110,  1.1466,  1.1466,
 1.1110,  1.0000,  0.7308,  0.2698, -0.2698, -0.7308, -1.0000, -1.1110,
-1.1466, -1.1466, -1.1508, -1.1510, -1.1508, -1.1466, -1.1466, -1.1508,
-1.1510, -1.1508, -1.1466, -1.1466, -1.1508, -1.1510, -1.1508, -1.1466,
-1.1466, -1.1508, -1.1510, -1.1508, -1.1466, -1.1466, -1.1508, -1.1510,
-1.1508, -1.1466, -1.1466, -1.1110, -1.0000, -0.7308, -0.2698,  0.2698,
 0.7109,  0.9245,  0.9010,  0.7082,  0.4384,  0.2100,  0.0755,  0.0199,
 0     ,  0     ,  0     ,  0
};

//============================================================================
cRxModem::cRxModem(int nRxChannelNo, float fRxReferMinVal, float fRxReferMidVal, float fRxReferMaxVal)
{
    m_nRxChannelNo  = nRxChannelNo;
    m_fRxReferMinVal= fRxReferMinVal;
    m_fRxReferMidVal= fRxReferMidVal;
    m_fRxReferMaxVal= fRxReferMaxVal;
    m_fRxReferValue = m_fRxReferMidVal;
    
    memset((void*)m_pRxRawDataBuff, 0x00, sizeof(float) * RX_RAW_DATA_BUFF_SIZE);
    m_dwRxRawDataIdx = 0;

    m_vRxRawFormBuff = (xAisRxRawForm*)SysAllocMemory(sizeof(xAisRxRawForm) * RX_RAW_FORM_BUFF_SIZE);

#if defined(RX_GMSK_FILTER_USE_MODE)
    memset((void *)m_pRxRawGmskBuff, 0x00, sizeof(float) * RX_GMSK_BT_0_5_FIR_N);
#endif

    m_wBitSamplCntr = 0;

    m_wRxRunStatus  = RX_MODEM_STATUS_PREAMBLE;
    m_wRxBitCount   = 0;
    m_wRxShiftReg   = 0;

    m_wReceivingID  = 0;
    m_wRxMaxBitSize = 168;

    m_nRxPrevBitD   = 0;
    m_nRxCurrBitD   = 0;

    m_wNewBitData   = 0;
    m_wCrcRegData   = 0;
    m_bRxByteData   = 0;

    m_fRxAfAdcData  = 0;

    m_dRxAdcErrCnt  = 0;

    m_fRxMovingSum  = 0;
    m_nRxDcLevelIdx = 0;

    m_fRxSyncPowSum = 0;
    m_fFilteredSyncSum = 0;
    m_fFilteredSyncPowSum = 0;
    for (int i = 0; i < RX_PREAMBLE_LEN; i++)
    {
        m_fRxSyncPowSum += (G_vGmskPreamble[i] * G_vGmskPreamble[i]);

        m_fFilteredSyncSum += G_vFilteredPreamble[FILTERED_PREAMBLE_OFFSET + i];
        m_fFilteredSyncPowSum += (G_vFilteredPreamble[FILTERED_PREAMBLE_OFFSET + i] * G_vFilteredPreamble[FILTERED_PREAMBLE_OFFSET + i]);
    }

    ClearPreambleBuff();
    ClearRxRawBuff();
} 

cRxModem::~cRxModem(void)
{
}

void cRxModem::ClearPreambleBuff(void)
{
    memset((void*)m_pRxPreambleBuff, 0x00, sizeof(float) * RX_PREAMBLE_LEN);
    memset((void *)m_vRxNormBuff, 0x00, sizeof(float) * RX_PREAMBLE_LEN);

    m_fRxNormAdc        = 0;
    m_dRxNormBuffIdx    = 0;
    m_fMaxCorrVal       = 0;
    m_dwMaxElapseCount  = 0;
}

void cRxModem::ClearRxRawBuff(void)
{
    memset((void *)m_vRxRawFormBuff, 0x00, sizeof(xAisRxRawForm)*RX_RAW_FORM_BUFF_SIZE);
    m_nRxRawFormHead = 0;
    m_nRxRawFormTail = 0;
    m_nRxRawFormTemp = 0;
}

void cRxModem::ClearRxRawFormTemp(void)
{
    m_nRxRawFormTemp = 0;
}

void cRxModem::ResetToRxStatusPreamble(void)
{
    m_wRxRunStatus = RX_MODEM_STATUS_PREAMBLE;
    m_wRxShiftReg  = 0;
    m_fRxReferValue= m_fRxReferMidVal;
}

#if defined(RX_GMSK_FILTER_USE_MODE)
float cRxModem::RunGmskFilter(float fInAdc)
{
    volatile float fOutAdc = 0;

    memmove((void *)&m_pRxRawGmskBuff[0], (void *)&m_pRxRawGmskBuff[1], sizeof(float) * (RX_GMSK_BT_0_5_FIR_N-1));
    m_pRxRawGmskBuff[RX_GMSK_BT_0_5_FIR_N - 1] = fInAdc;

    // Convolution with GMSK filter coefficients
    for (int i = 0; i < RX_GMSK_BT_0_5_FIR_N; i++)
    {
        fOutAdc += (m_pRxRawGmskBuff[i] * G_vRxGmskCoefficient[i]);
    }

#if defined(__DEBUG_RXMODEM_FILTER__) && defined(DBG_SET_TEST_CHANNEL)
    if(m_nRxChannelNo == DBG_SET_TEST_CHANNEL)
        cDac::getInst()->SetDAC1Data(fOutAdc);
#endif

    return fOutAdc;
}
#endif

bool cRxModem::RunDetectPreamble(float fInAdc)
{
	float fCrossSync = 0;
	float fSyncCorrVal  = 0;
	int   nBuffIdx   = 0;
	bool bDetected = false;

    // Pre calculate power value
    m_fRxNormAdc -= (m_vRxNormBuff[m_dRxNormBuffIdx] * m_vRxNormBuff[m_dRxNormBuffIdx]);
    m_fRxNormAdc += (fInAdc * fInAdc);
    m_vRxNormBuff[m_dRxNormBuffIdx] = fInAdc;

    m_dRxNormBuffIdx++;
    if (m_dRxNormBuffIdx >= RX_PREAMBLE_LEN)
    {
        m_dRxNormBuffIdx = 0;
    }

    // convolution with preamble
    nBuffIdx = m_dRxNormBuffIdx;
    for (int i = 0; i < RX_PREAMBLE_LEN; i++)
    {
        fCrossSync += (m_vRxNormBuff[nBuffIdx] * G_vGmskPreamble[i]);

        nBuffIdx++;
        if (nBuffIdx >= RX_PREAMBLE_LEN)
            nBuffIdx = 0;
    }

    // calculate correlation value
    if (m_fRxNormAdc != 0 && m_fRxSyncPowSum != 0)
        fSyncCorrVal = (fCrossSync * fCrossSync) / (m_fRxNormAdc * m_fRxSyncPowSum);
    else
        fSyncCorrVal = 0;

    // find correlation peak point
    if ((fSyncCorrVal > RX_SYNC_THRESHOLD) && (fSyncCorrVal > m_fMaxCorrVal))
    {
        m_fMaxCorrVal = fSyncCorrVal;
        m_dwMaxCorrIdx = m_dwRxRawDataIdx;
        m_dwMaxElapseCount = 0;

        // Set data start index
        if (m_dwRxRawDataIdx + RX_DATA_OFFSET_CNT >= 0)
            m_dwRxDataStartIdx = m_dwRxRawDataIdx + RX_DATA_OFFSET_CNT;
        else
            m_dwRxDataStartIdx = RX_RAW_DATA_BUFF_SIZE + m_dwRxRawDataIdx + RX_DATA_OFFSET_CNT;

        // Calculate DC reference value
        if ((m_nRxDcLevelIdx - RX_START_FLAG_BIT_LEN) >= 0)
            m_fRxReferValue = m_vRxDcLevelBuff[m_nRxDcLevelIdx - RX_START_FLAG_BIT_LEN];
        else
            m_fRxReferValue = m_vRxDcLevelBuff[RX_TRAINING_BIT_LEN + m_nRxDcLevelIdx - RX_START_FLAG_BIT_LEN];
    }
    else if (m_fMaxCorrVal > RX_SYNC_THRESHOLD)
    {
        m_dwMaxElapseCount++;

        // Confirm peak
        if (m_dwMaxElapseCount >= RX_SYNC_STABLE_CNT)
        {
            bDetected = true;
            m_wRxRunStatus = RX_MODEM_STATUS_START;
            m_nRxOvsCnt = RX_OVERSAMPLE_RATE;
            m_wRxBitCount = 0;
        }
    }
    else
    {
        m_fMaxCorrVal = 0;
        m_dwMaxElapseCount = 0;
    }

    return bDetected;
}


// 채널 임펄스 응답의 중심 계수 : 현재 전송되는 심볼이 수신 신호에 미치는 영향의 크기
// ISI(Inter - Symbol Interference) 모델링 : h1과 함께 사용되어 이전 / 이후 심볼이 현재 심볼에 미치는 간섭을 모델링
// Viterbi MLSD 알고리즘의 핵심 파라미터 : 채널 특성을 반영하여 최적의 비트 시퀀스를 복원하는데 사용
void cRxModem::EstimateSignalGain(
    const float* fRcvPreamble,
    const float* fSrcPreamble,
    int nNum,
    float  fMaxImpulseResponse,
    float* fH0,
    float* fBias
) {
    float fRcvPowSum = 0.0f, fRcvSum = 0.0f;
    for (int i = 0; i < nNum; i++) {
        fRcvPowSum += fSrcPreamble[i] * fRcvPreamble[i];
        fRcvSum += fRcvPreamble[i];
    }

    float fDetVal = m_fFilteredSyncPowSum * nNum - m_fFilteredSyncSum * m_fFilteredSyncSum;
    float fH0Val = 0.0f, fBiasVal = 0.0f;

    // if (fabsf(fDetVal) > 1e-10f) {
        float h0_scale = ((nNum * fRcvPowSum) - (m_fFilteredSyncSum * fRcvSum)) / fDetVal;
        fBiasVal = ((m_fFilteredSyncPowSum * fRcvSum) - (m_fFilteredSyncSum * fRcvPowSum)) / fDetVal;
        fH0Val = h0_scale * fMaxImpulseResponse;
    }
    else {
        fBiasVal = (fRcvSum / nNum) - (m_fFilteredSyncSum / nNum);

        float fSumDc = 0.0f;
        for (int i = 0; i < nNum; i++) {
            float r_dc = fRcvPreamble[i] - fBiasVal;
            fSumDc += r_dc * fSrcPreamble[i];
        }

        float fH0Scale = fSumDc / m_fFilteredSyncPowSum;
        fH0Val = fH0Scale * fMaxImpulseResponse;
    }

    *fH0 = fabsf(fH0Val);
    *fBias = fabsf(fBiasVal);
}

/**
 * 소스 시퀀스를 복사하고 새 비트 추가
 */
void cRxModem::CopySeqAndAddBit(const uint8_t* src_sequence, int src_length,
                                int* dst_sequence, int* dst_length, int bit)
{
    *dst_length = 0;

    // 기존 시퀀스 복사
    for (int i = 0; i < src_length && i < VITERBI_MAX_SEQ_LEN - 1; i++) {
        dst_sequence[i] = src_sequence[i];
        (*dst_length)++;
    }

    // 새 비트 추가
    if (*dst_length < VITERBI_MAX_SEQ_LEN) {
        dst_sequence[*dst_length] = bit;
        (*dst_length)++;
    }
}

#if 1
int8_t cRxModem::ViterbiMlsd(ViterbiStates* states, float rx_data,
    float main_signal_coeff, float isi_signal_coeff) {
    
    // ISI를 고려한 8가지 예상 신호 값 계산
    double h_values[8] = {
        -isi_signal_coeff - main_signal_coeff - isi_signal_coeff,  // [-1,-1,-1]
        -isi_signal_coeff - main_signal_coeff + isi_signal_coeff,  // [-1,-1,+1]
        -isi_signal_coeff + main_signal_coeff - isi_signal_coeff,  // [-1,+1,-1]
        -isi_signal_coeff + main_signal_coeff + isi_signal_coeff,  // [-1,+1,+1]
         isi_signal_coeff - main_signal_coeff - isi_signal_coeff,  // [+1,-1,-1]
         isi_signal_coeff - main_signal_coeff + isi_signal_coeff,  // [+1,-1,+1]
         isi_signal_coeff + main_signal_coeff - isi_signal_coeff,  // [+1,+1,-1]
         isi_signal_coeff + main_signal_coeff + isi_signal_coeff   // [+1,+1,+1]
    };

    // 상태 전이 테이블: [현재상태][입력비트] = {다음상태, h_values인덱스}
    struct { int next_state; int h_idx; } transitions[4][2] = {
        {{0, 0}, {1, 1}},  // 상태 00: 비트0→상태00, 비트1→상태01
        {{2, 2}, {3, 3}},  // 상태 01: 비트0→상태10, 비트1→상태11
        {{0, 4}, {1, 5}},  // 상태 10: 비트0→상태00, 비트1→상태01
        {{2, 6}, {3, 7}}   // 상태 11: 비트0→상태10, 비트1→상태11
    };

    double new_path_metrics[VITERBI_NUM_STATES];
    int new_sequences[VITERBI_NUM_STATES][VITERBI_MAX_SEQ_LEN];
    int new_seq_len[VITERBI_NUM_STATES];

    // 각 상태별로 최적 경로 선택
    for (int next_state = 0; next_state < VITERBI_NUM_STATES; next_state++) {
        double best_metric = 1e10;
        int best_prev_state = 0;
        int best_bit = 0;

        // 현재 상태로 올 수 있는 모든 경로 검사
        for (int prev_state = 0; prev_state < VITERBI_NUM_STATES; prev_state++) {
            for (int bit = 0; bit < 2; bit++) {
                if (transitions[prev_state][bit].next_state == next_state) {
                    double branch_metric = (rx_data - h_values[transitions[prev_state][bit].h_idx]);
                    branch_metric *= branch_metric;
                    double path_metric = states->path_metrics[prev_state] + branch_metric;
                    
                    if (path_metric < best_metric) {
                        best_metric = path_metric;
                        best_prev_state = prev_state;
                        best_bit = bit;
                    }
                }
            }
        }

        // 최적 경로 저장
        new_path_metrics[next_state] = best_metric;
        CopySeqAndAddBit(states->sequences[best_prev_state], states->seq_len[best_prev_state],
            new_sequences[next_state], &new_seq_len[next_state], best_bit);
    }

    // 상태 업데이트
    for (int i = 0; i < VITERBI_NUM_STATES; i++) {
        states->path_metrics[i] = new_path_metrics[i];
        states->seq_len[i] = new_seq_len[i];
        for (int j = 0; j < new_seq_len[i]; j++) {
            states->sequences[i][j] = new_sequences[i][j];
        }
    }

    // Early decision
    if (states->seq_len[0] > 0 && states->seq_len[1] > 0 &&
        states->seq_len[2] > 0 && states->seq_len[3] > 0) {
        
        uint8_t decided_bit = states->sequences[0][0];
        if (states->sequences[1][0] == decided_bit &&
            states->sequences[2][0] == decided_bit &&
            states->sequences[3][0] == decided_bit) {

            // Shift all sequences left by 1 position
            for (int s = 0; s < VITERBI_NUM_STATES; s++) {
                if (--states->seq_len[s] > 0) {
                    memmove(states->sequences[s], states->sequences[s] + 1, states->seq_len[s]);
                }
            }
            return decided_bit;
        }
    }

    return -1;
}

#else
/**
 * Viterbi MLSD 알고리즘 구현
 * ISI를 고려한 Maximum Likelihood Sequence Detection
 */
int8_t cRxModem::ViterbiMlsd(ViterbiStates* states, float rx_data,
    float main_signal_coeff, float isi_signal_coeff) {

    // ISI를 고려한 8가지 예상 신호 값 계산
    // 형태: [이전비트, 현재비트, 다음비트]의 조합에 따른 신호값
    double h_values[8];
    h_values[0] = -isi_signal_coeff - main_signal_coeff - isi_signal_coeff;  // [-1,-1,-1]
    h_values[1] = -isi_signal_coeff - main_signal_coeff + isi_signal_coeff;  // [-1,-1,+1]
    h_values[2] = -isi_signal_coeff + main_signal_coeff - isi_signal_coeff;  // [-1,+1,-1]
    h_values[3] = -isi_signal_coeff + main_signal_coeff + isi_signal_coeff;  // [-1,+1,+1]
    h_values[4] = isi_signal_coeff - main_signal_coeff - isi_signal_coeff;  // [+1,-1,-1]
    h_values[5] = isi_signal_coeff - main_signal_coeff + isi_signal_coeff;  // [+1,-1,+1]
    h_values[6] = isi_signal_coeff + main_signal_coeff - isi_signal_coeff;  // [+1,+1,-1]
    h_values[7] = isi_signal_coeff + main_signal_coeff + isi_signal_coeff;  // [+1,+1,+1]

    // 새로운 상태 정보를 저장할 임시 변수들
    double new_path_metrics[VITERBI_NUM_STATES];
    int new_sequences[VITERBI_NUM_STATES][VITERBI_MAX_SEQ_LEN];
    int new_seq_len[VITERBI_NUM_STATES];

    // ========== 상태 00 (이전 2비트: 00) 처리 ==========
    // 상태 00으로 들어올 수 있는 경로:
    // 1) 상태 00에서 비트 0 입력 (00 → 00)
    // 2) 상태 10에서 비트 0 입력 (10 → 00)

    double bm_00_0 = pow(rx_data - h_values[0], 2);  // 상태 00→00 (비트패턴: 000)
    double bm_10_0 = pow(rx_data - h_values[4], 2);  // 상태 10→00 (비트패턴: 100)

    double pm_cand1 = states->path_metrics[0] + bm_00_0;  // 상태 00에서 오는 경로
    double pm_cand2 = states->path_metrics[2] + bm_10_0;  // 상태 10에서 오는 경로

    // 생존 경로 선택 (Add-Compare-Select)
    if (pm_cand1 < pm_cand2) {
        new_path_metrics[0] = pm_cand1;
        CopySeqAndAddBit(states->sequences[0], states->seq_len[0],
            new_sequences[0], &new_seq_len[0], 0);
    }
    else {
        new_path_metrics[0] = pm_cand2;
        CopySeqAndAddBit(states->sequences[2], states->seq_len[2],
            new_sequences[0], &new_seq_len[0], 0);
    }

    // ========== 상태 01 (이전 2비트: 01) 처리 ==========
    // 상태 01로 들어올 수 있는 경로:
    // 1) 상태 00에서 비트 1 입력 (00 → 01)
    // 2) 상태 10에서 비트 1 입력 (10 → 01)

    double bm_00_1 = pow(rx_data - h_values[1], 2);  // 상태 00→01 (비트패턴: 001)
    double bm_10_1 = pow(rx_data - h_values[5], 2);  // 상태 10→01 (비트패턴: 101)

    pm_cand1 = states->path_metrics[0] + bm_00_1;  // 상태 00에서 오는 경로
    pm_cand2 = states->path_metrics[2] + bm_10_1;  // 상태 10에서 오는 경로

    if (pm_cand1 < pm_cand2) {
        new_path_metrics[1] = pm_cand1;
        CopySeqAndAddBit(states->sequences[0], states->seq_len[0],
            new_sequences[1], &new_seq_len[1], 1);
    }
    else {
        new_path_metrics[1] = pm_cand2;
        CopySeqAndAddBit(states->sequences[2], states->seq_len[2],
            new_sequences[1], &new_seq_len[1], 1);
    }

    // ========== 상태 10 (이전 2비트: 10) 처리 ==========
    // 상태 10으로 들어올 수 있는 경로:
    // 1) 상태 01에서 비트 0 입력 (01 → 10)
    // 2) 상태 11에서 비트 0 입력 (11 → 10)

    double bm_01_0 = pow(rx_data - h_values[2], 2);  // 상태 01→10 (비트패턴: 010)
    double bm_11_0 = pow(rx_data - h_values[6], 2);  // 상태 11→10 (비트패턴: 110)

    pm_cand1 = states->path_metrics[1] + bm_01_0;  // 상태 01에서 오는 경로
    pm_cand2 = states->path_metrics[3] + bm_11_0;  // 상태 11에서 오는 경로

    if (pm_cand1 < pm_cand2) {
        new_path_metrics[2] = pm_cand1;
        CopySeqAndAddBit(states->sequences[1], states->seq_len[1],
            new_sequences[2], &new_seq_len[2], 0);
    }
    else {
        new_path_metrics[2] = pm_cand2;
        CopySeqAndAddBit(states->sequences[3], states->seq_len[3],
            new_sequences[2], &new_seq_len[2], 0);
    }

    // ========== 상태 11 (이전 2비트: 11) 처리 ==========
    // 상태 11로 들어올 수 있는 경로:
    // 1) 상태 01에서 비트 1 입력 (01 → 11)
    // 2) 상태 11에서 비트 1 입력 (11 → 11)

    double bm_01_1 = pow(rx_data - h_values[3], 2);  // 상태 01→11 (비트패턴: 011)
    double bm_11_1 = pow(rx_data - h_values[7], 2);  // 상태 11→11 (비트패턴: 111)

    pm_cand1 = states->path_metrics[1] + bm_01_1;  // 상태 01에서 오는 경로
    pm_cand2 = states->path_metrics[3] + bm_11_1;  // 상태 11에서 오는 경로

    if (pm_cand1 < pm_cand2) {
        new_path_metrics[3] = pm_cand1;
        CopySeqAndAddBit(states->sequences[1], states->seq_len[1],
            new_sequences[3], &new_seq_len[3], 1);
    }
    else {
        new_path_metrics[3] = pm_cand2;
        CopySeqAndAddBit(states->sequences[3], states->seq_len[3],
            new_sequences[3], &new_seq_len[3], 1);
    }

    // ========== 다음 시간 단계를 위한 상태 업데이트 ==========
    for (int i = 0; i < VITERBI_NUM_STATES; i++) {
        states->path_metrics[i] = new_path_metrics[i];
        states->seq_len[i] = new_seq_len[i];

        for (int j = 0; j < new_seq_len[i]; j++) {
            states->sequences[i][j] = new_sequences[i][j];
        }
    }

    // Early decision with bounds check
    if (states->seq_len[0] > 0 && states->seq_len[1] > 0 &&
        states->seq_len[2] > 0 && states->seq_len[3] > 0)
    {
        uint8_t decided_bit = states->sequences[0][0];
        if (states->sequences[1][0] == decided_bit &&
            states->sequences[2][0] == decided_bit &&
            states->sequences[3][0] == decided_bit) {

            // Shift all sequences left by 1 position
            for (int s = 0; s < VITERBI_NUM_STATES; s++) {
                if (--states->seq_len[s] > 0) {
                    memmove(states->sequences[s], states->sequences[s] + 1, states->seq_len[s]);
                }
            }
            return decided_bit;
        }
    }

    return -1;
}

#endif

void cRxModem::ApplyGmskFilter(HWORD wRxAfAdcData)
{
#if defined(RX_GMSK_FILTER_USE_MODE)
    m_fRxAfAdcData = RunGmskFilter(wRxAfAdcData);
#else
    m_fRxAfAdcData = wRxAfAdcData;
#endif
}

void cRxModem::BufferRawData(void)
{
    if (++m_dwRxRawDataIdx >= RX_RAW_DATA_BUFF_SIZE)
        m_dwRxRawDataIdx = 0;
    
    m_pRxRawDataBuff[m_dwRxRawDataIdx] = m_fRxAfAdcData;
}

void cRxModem::NormalizeDcLevel(void)
{
    // Update moving average filter
    int prevIdx = (m_dwRxRawDataIdx >= RX_TRAINING_BIT_LEN) ? 
        m_dwRxRawDataIdx - RX_TRAINING_BIT_LEN : 
        RX_RAW_DATA_BUFF_SIZE - (RX_TRAINING_BIT_LEN - m_dwRxRawDataIdx);
    
    m_fRxMovingSum -= m_pRxRawDataBuff[prevIdx];
    m_fRxMovingSum += m_pRxRawDataBuff[m_dwRxRawDataIdx];
    
    if (++m_nRxDcLevelIdx >= RX_TRAINING_BIT_LEN)
        m_nRxDcLevelIdx = 0;
    
    m_vRxDcLevelBuff[m_nRxDcLevelIdx] = m_fRxMovingSum / RX_TRAINING_BIT_LEN;
    
    // Remove DC offset
    m_fRxAfAdcData -= m_vRxDcLevelBuff[m_nRxDcLevelIdx];
}

void cRxModem::EstimateChannelParameters(void)
{
    float fSignalGain = 0.0f;
    float fBias = 0.0f;
    int nPreambleIdx = (m_dwMaxCorrIdx > 0) ? m_dwMaxCorrIdx - 1 : RX_RAW_DATA_BUFF_SIZE - 1;
    
    for (int idx = 0; idx < RX_PREAMBLE_LEN; idx++)
    {
        m_pRxPreambleBuff[RX_PREAMBLE_LEN - idx - 1] = 
            m_pRxRawDataBuff[nPreambleIdx] - m_fRxReferValue;
        
        nPreambleIdx = (nPreambleIdx > 0) ? nPreambleIdx - 1 : RX_RAW_DATA_BUFF_SIZE - 1;
    }
    
    EstimateSignalGain(
        (float*)m_pRxPreambleBuff,
        &G_vFilteredPreamble[FILTERED_PREAMBLE_OFFSET],
        RX_PREAMBLE_LEN,
        GMSK_MAX_IMPULSE_RESPONSE,
        &fSignalGain,
        &fBias
    );
    
    m_fSignalGain = GMSK_MAX_IMPULSE_RESPONSE * fSignalGain;
    m_fSignalCoff = GMSK_ISI_IMPULSE_RESPONSE * fSignalGain;
}

void cRxModem::InitializeViterbiDecoder(void)
{
    memset((void*)&m_vViterbiStates, 0x00, sizeof(ViterbiStates));
    m_bFirstBitSkipped = false;
    m_wBitSamplCntr = 0;
}

void  cRxModem::WritePacketIntoRxRawBuff(void)
{
    if(m_nRxRawFormTemp == 0 || m_wRxBitCount < 40)
        return;
    m_vRxRawFormBuff[m_nRxRawFormHead].wMsgFrameNO    = cAisModem::getInst()->GetCurrentFrameNo();
    m_vRxRawFormBuff[m_nRxRawFormHead].dSlotNoCounter = m_dSlotNoCounter;
    m_vRxRawFormBuff[m_nRxRawFormHead].dSampleCounter = m_dSampleCounter;
    m_vRxRawFormBuff[m_nRxRawFormHead].wRxByteSize    = m_nRxRawFormTemp -  2; // - FCS (2-bytes)
    m_vRxRawFormBuff[m_nRxRawFormHead].wRxBitsSize    = m_wRxBitCount    - 16; // - FCS (16-bits)

    ++m_nRxRawFormHead;
    if(m_nRxRawFormHead >= RX_RAW_FORM_BUFF_SIZE)
        m_nRxRawFormHead  = 0;
}

void  cRxModem::PutDataIntoRxRawBuff(UCHAR bRxData)
{
    if(m_nRxRawFormTemp < (AIS_MAX_REAL_RAW_BYTE_PER_ONE_RX - 2))
        m_vRxRawFormBuff[m_nRxRawFormHead].vRxRawData[m_nRxRawFormTemp++] = bRxData;
}

int cRxModem::ProcessRxDataCommonRun(void)
{
    m_wRxShiftReg <<= 1;

    if (m_nRxCurrBitD == m_nRxPrevBitD)
    {
        m_wRxShiftReg |= 0x0001;
    }
    else
    {
        m_wRxShiftReg &= 0xfffe;
    }

    m_nRxPrevBitD = m_nRxCurrBitD;

    switch (m_wRxRunStatus)
    {
    case RX_MODEM_STATUS_PRELOAD:
        if(++m_wRxBitCount == 8) {
            m_wRxBitCount = 0;
            m_wCrcRegData = 0xffff;
            m_wRxRunStatus= RX_MODEM_STATUS_DATA;
            ClearRxRawFormTemp();

            int nMsgID = GetReverseBitValue((m_wRxShiftReg << 2) & 0xff);
            if(nMsgID < 0 || nMsgID > 27) {
                ResetToRxStatusPreamble();
            }
            else {
                m_wReceivingID  = nMsgID;
                m_wRxMaxBitSize = CAisLib::GetAisRxMsgMaxBitSizeByMsgID(m_wReceivingID);
            }
       }
       break;

    case RX_MODEM_STATUS_DATA:
        if((m_wRxShiftReg & 0x3f00) != 0x3e00)        // It's not a stuffing bit
        {
            ++m_wRxBitCount;
            if(m_wRxBitCount >= m_wRxMaxBitSize) {
                ResetToRxStatusPreamble();
                return 0;
            }

            m_wNewBitData = (m_wRxShiftReg >> 8) & 0x0001;
            m_bRxByteData = (m_bRxByteData >> 1) | ((m_wRxShiftReg >> 1) & 0x0080);

            if(!(m_wRxBitCount & 0x07))
                PutDataIntoRxRawBuff(m_bRxByteData);

            if((m_wCrcRegData ^ m_wNewBitData) & 0x0001)          // Pass new bit through CRC calculation
                m_wCrcRegData = (m_wCrcRegData >> 1) ^ 0x8408;    // Xor with the CRC polynomial (X^16 + X^12 + X^5 + 1)
            else
                m_wCrcRegData >>= 1;
        }

        if((m_wRxShiftReg & 0x00ff) == 0x007e) {
            if(m_wCrcRegData == 0xf0b8) {    // This should give a result of 0xF0B8
                WritePacketIntoRxRawBuff();
                ResetToRxStatusPreamble();
            }
            else {
                m_dSampleCounter = cAisModem::getInst()->GetSampleCounter();
                m_dSlotNoCounter = cAisModem::getInst()->GetSlotNoCounter();

                m_wRxBitCount = 0;
                ResetToRxStatusPreamble();
            }
        }
        break;

    default:
        break;
    }

    return 0;
}

void cRxModem::DecodeCurrentSample(void)
{
    // Get raw data excluding DC bias
    float fRxAfAdcRawX = m_pRxRawDataBuff[m_dwRxDataStartIdx] - m_fRxReferValue;

    // Decode current sample
    m_nRxCurrBitD = ViterbiMlsd(
        (ViterbiStates*)&m_vViterbiStates, 
        fRxAfAdcRawX, 
        m_fSignalGain, 
        m_fSignalCoff
    );
    
    if (m_nRxCurrBitD >= 0)
    {
        // If it's the first bit, skip it
        if (!m_bFirstBitSkipped)
        {
            m_bFirstBitSkipped = true;
            m_nRxPrevBitD = m_nRxCurrBitD;
        }
        else
        {
            ProcessRxDataCommonRun();
        }
    }
}

void cRxModem::ProcessPreambleDetection(void)
{
    if (RunDetectPreamble(m_fRxAfAdcData))
        ClearPreambleBuff();
}

void cRxModem::ProcessStartSequence(void)
{
    EstimateChannelParameters();
    InitializeViterbiDecoder();
    m_wRxRunStatus = RX_MODEM_STATUS_PRELOAD;
}

void cRxModem::ProcessDataDecoding(void)
{
    do {
        if (++m_nRxOvsCnt >= RX_OVERSAMPLE_RATE)
        {
            DecodeCurrentSample();
            m_nRxOvsCnt = 0;
        }
        
        if (++m_dwRxDataStartIdx >= RX_RAW_DATA_BUFF_SIZE)
            m_dwRxDataStartIdx = 0;
    } while (m_dwRxDataStartIdx != m_dwRxRawDataIdx);
}

int cRxModem::ProcessGmskRxData(HWORD wRxAfAdcData)
{
    // Apply GMSK filter if enabled
    ApplyGmskFilter(wRxAfAdcData);

    // Buffering rx raw data
    BufferRawData();

    // Normalize DC level
    NormalizeDcLevel();

    // Process based on current receiver status
    switch (m_wRxRunStatus)
    {
        case RX_MODEM_STATUS_PREAMBLE:
            ProcessPreambleDetection();
            break;
            
        case RX_MODEM_STATUS_START:
            ProcessStartSequence();
            break;
            
        default:
            ProcessDataDecoding();
            break;
    }

    return 0;
}

xAisRxRawForm *cRxModem::GetFullPacketFromRxRawBuff(void)
{
    xAisRxRawForm *pAisRxRawForm;

    if(m_nRxRawFormHead == m_nRxRawFormTail)
        return(NULL);

    pAisRxRawForm = (xAisRxRawForm*)&m_vRxRawFormBuff[m_nRxRawFormTail];

    ++m_nRxRawFormTail;
    if(m_nRxRawFormTail >= RX_RAW_FORM_BUFF_SIZE)
        m_nRxRawFormTail  = 0;

    return(pAisRxRawForm);
}
